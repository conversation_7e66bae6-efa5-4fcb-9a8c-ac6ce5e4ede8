<?php
/**
 * Store Bot Database Setup Script
 * این فایل دیتابیس را به صورت خودکار نصب و بروزرسانی می‌کند
 * 
 * نحوه استفاده:
 * 1. تنظیمات دیتابیس را در بخش config تنظیم کنید
 * 2. فایل را در مرورگر اجرا کنید: http://yoursite.com/database_setup.php
 * 3. یا از command line: php database_setup.php
 * 
 * @version 1.0
 * @date 2025-07-31
 */

// بارگذاری تنظیمات از فایل جداگانه
if (file_exists('database_config.php')) {
    $settings = include 'database_config.php';
    $config = $settings['database'];
    $security = $settings['security'];
    $options = $settings['options'] ?? [];
} else {
    // تنظیمات پیش‌فرض در صورت عدم وجود فایل config
    $config = [
        'host' => 'localhost',
        'username' => 'root',
        'password' => '',
        'database' => 'store_bot_db',
        'charset' => 'utf8mb4'
    ];

    $security = [
        'allowed_ips' => ['127.0.0.1', '::1'],
        'require_auth' => false,
        'auth_password' => 'setup123'
    ];

    $options = [
        'show_errors' => true,
        'log_operations' => true
    ];

    echo "⚠️ فایل database_config.php یافت نشد، از تنظیمات پیش‌فرض استفاده می‌شود\n";
}

// بررسی امنیت
function checkSecurity($security) {
    // بررسی IP
    $client_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    if (!empty($security['allowed_ips']) && !in_array($client_ip, $security['allowed_ips'])) {
        die("❌ دسترسی غیرمجاز! IP شما: $client_ip");
    }
    
    // بررسی رمز عبور
    if ($security['require_auth']) {
        $password = $_GET['password'] ?? $_POST['password'] ?? '';
        if ($password !== $security['auth_password']) {
            die("❌ رمز عبور اشتباه است! استفاده کنید: ?password=" . $security['auth_password']);
        }
    }
}

// اتصال به دیتابیس
function connectDatabase($config) {
    try {
        $dsn = "mysql:host={$config['host']};charset={$config['charset']}";
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']}"
        ]);
        
        echo "✅ اتصال به MySQL برقرار شد\n";
        return $pdo;
    } catch (PDOException $e) {
        die("❌ خطا در اتصال به دیتابیس: " . $e->getMessage());
    }
}

// ایجاد دیتابیس
function createDatabase($pdo, $dbName) {
    try {
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$dbName`");
        echo "✅ دیتابیس '$dbName' آماده است\n";
    } catch (PDOException $e) {
        die("❌ خطا در ایجاد دیتابیس: " . $e->getMessage());
    }
}

// اجرای کوئری‌های SQL
function executeSQL($pdo, $sql, $description) {
    try {
        $pdo->exec($sql);
        echo "✅ $description\n";
        return true;
    } catch (PDOException $e) {
        echo "⚠️ خطا در $description: " . $e->getMessage() . "\n";
        return false;
    }
}

// بررسی وجود ستون
function columnExists($pdo, $table, $column) {
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = ?
            AND COLUMN_NAME = ?
        ");
        $stmt->execute([$table, $column]);
        $result = $stmt->fetch();
        return $result['count'] > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// تعریف تیبل‌ها
function getTableDefinitions() {
    return [
        'apis' => "
            CREATE TABLE IF NOT EXISTS `apis` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `token` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `site` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `status` int(255) NOT NULL DEFAULT 0,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci
        ",
        
        'autoorders' => "
            CREATE TABLE IF NOT EXISTS `autoorders` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `uid` varchar(1024) DEFAULT NULL,
                `pid` int(255) DEFAULT NULL,
                `cid` varchar(1024) DEFAULT NULL,
                `quantity` int(255) DEFAULT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ",
        
        'category' => "
            CREATE TABLE IF NOT EXISTS `category` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                `parent` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0',
                `api` int(7) DEFAULT NULL,
                `creator` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `sort` int(255) NOT NULL DEFAULT 1000,
                `checked` int(10) NOT NULL DEFAULT 1,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci
        ",
        
        'manual' => "
            CREATE TABLE IF NOT EXISTS `manual` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `uid` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                `pid` int(255) NOT NULL,
                `cost` float NOT NULL,
                `time` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0',
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci
        ",
        
        'orders' => "
            CREATE TABLE IF NOT EXISTS `orders` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                `name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                `quantity` int(255) NOT NULL,
                `link` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                `cost` int(255) NOT NULL,
                `time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                `track` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `status` int(255) NOT NULL DEFAULT 0,
                `agency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `start_count` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0',
                `remains` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0',
                `api` int(5) NOT NULL DEFAULT 0,
                `msgid` int(255) NOT NULL DEFAULT 0,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci
        ",
        
        'payment' => "
            CREATE TABLE IF NOT EXISTS `payment` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                `cost` int(255) NOT NULL,
                `time` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                `agency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci
        ",

        'product' => "
            CREATE TABLE IF NOT EXISTS `product` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                `price` float DEFAULT NULL,
                `old_price` float DEFAULT NULL,
                `category` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `min` int(255) DEFAULT NULL,
                `max` int(255) DEFAULT NULL,
                `des` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `sid` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `api` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `code` int(255) DEFAULT NULL,
                `creator` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `sort` int(255) NOT NULL DEFAULT 1000,
                `status` int(255) NOT NULL DEFAULT 1,
                `checked` int(5) NOT NULL DEFAULT 1,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci
        ",

        'sendall' => "
            CREATE TABLE IF NOT EXISTS `sendall` (
                `id` int(255) NOT NULL AUTO_INCREMENT,
                `step` varchar(20) DEFAULT NULL,
                `text` text DEFAULT NULL,
                `chat` varchar(100) DEFAULT NULL,
                `user` int(11) DEFAULT 0,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ",

        'tusers' => "
            CREATE TABLE IF NOT EXISTS `tusers` (
                `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                `first_name` varchar(1023) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                `last_name` varchar(1023) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `username` varchar(1023) DEFAULT NULL,
                `step` varchar(16) DEFAULT NULL,
                `invite` int(127) NOT NULL DEFAULT 0,
                `invited_by` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `wallet` int(127) NOT NULL DEFAULT 0,
                `invite_earn` int(127) NOT NULL DEFAULT 0,
                `last_order` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `last_number` bigint(20) DEFAULT NULL,
                `number` int(30) NOT NULL DEFAULT 0,
                `send` int(255) NOT NULL DEFAULT 0,
                `status` int(10) NOT NULL DEFAULT 0,
                `phone` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `last_sms` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                PRIMARY KEY (`user_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci
        ",

        'user' => "
            CREATE TABLE IF NOT EXISTS `user` (
                `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                `name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                `username` varchar(1024) DEFAULT NULL,
                `wallet` int(255) NOT NULL DEFAULT 0,
                `step` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'home',
                `datash` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `datam` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `agency` int(255) NOT NULL DEFAULT 0,
                `earn` int(255) NOT NULL DEFAULT 0,
                `agencyTotal` int(255) NOT NULL DEFAULT 0,
                `earnTotal` int(255) NOT NULL DEFAULT 0,
                `invite` int(16) NOT NULL DEFAULT 0,
                `invitedBy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `token` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                `status` int(16) NOT NULL DEFAULT 0,
                `phone` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                `lastSMS` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                PRIMARY KEY (`uid`)
            ) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci
        ",

        'nowpayments' => "
            CREATE TABLE IF NOT EXISTS `nowpayments` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `uid` bigint(20) NOT NULL,
                `payment_id` varchar(255) NOT NULL,
                `order_id` varchar(255) NOT NULL,
                `price_amount` decimal(10,2) NOT NULL,
                `price_currency` varchar(10) NOT NULL DEFAULT 'usd',
                `pay_amount` decimal(20,8) NOT NULL,
                `pay_currency` varchar(20) NOT NULL,
                `pay_address` text NOT NULL,
                `payment_status` varchar(50) NOT NULL DEFAULT 'waiting',
                `actually_paid` decimal(20,8) DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `payment_id` (`payment_id`),
                KEY `uid` (`uid`),
                KEY `payment_status` (`payment_status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];
}

// بروزرسانی ستون‌ها
function updateColumns($pdo) {
    $updates = [];

    // اضافه کردن payment_type به جدول payment
    if (!columnExists($pdo, 'payment', 'payment_type')) {
        $sql = "ALTER TABLE `payment` ADD COLUMN `payment_type` VARCHAR(50) DEFAULT 'manual' AFTER `agency`";
        if (executeSQL($pdo, $sql, "اضافه کردن ستون payment_type به جدول payment")) {
            $updates[] = "payment_type به payment";
        }
    } else {
        echo "ℹ️ ستون payment_type در جدول payment موجود است\n";
    }

    // اضافه کردن datap به جدول user
    if (!columnExists($pdo, 'user', 'datap')) {
        $sql = "ALTER TABLE `user` ADD COLUMN `datap` VARCHAR(255) DEFAULT NULL AFTER `datam`";
        if (executeSQL($pdo, $sql, "اضافه کردن ستون datap به جدول user")) {
            $updates[] = "datap به user";
        }
    } else {
        echo "ℹ️ ستون datap در جدول user موجود است\n";
    }

    return $updates;
}

// اضافه کردن داده‌های اولیه
function insertInitialData($pdo) {
    $sql = "INSERT IGNORE INTO `sendall` (`id`, `step`, `text`, `chat`, `user`) VALUES (1, 'none', '', '', 0)";
    executeSQL($pdo, $sql, "اضافه کردن داده اولیه به جدول sendall");
}

// تابع اصلی setup
function setupDatabase($config, $security) {
    echo "<pre style='direction: rtl; text-align: right; font-family: Tahoma;'>\n";
    echo "🚀 شروع نصب دیتابیس Store Bot\n";
    echo "=====================================\n\n";

    // بررسی امنیت
    checkSecurity($security);
    echo "✅ بررسی امنیت انجام شد\n\n";

    // اتصال به دیتابیس
    $pdo = connectDatabase($config);

    // ایجاد دیتابیس
    createDatabase($pdo, $config['database']);
    echo "\n";

    // ایجاد تیبل‌ها
    echo "📋 ایجاد تیبل‌ها:\n";
    echo "-------------------\n";
    $tables = getTableDefinitions();
    foreach ($tables as $tableName => $sql) {
        executeSQL($pdo, $sql, "ایجاد جدول $tableName");
    }
    echo "\n";

    // بروزرسانی ستون‌ها
    echo "🔄 بروزرسانی ستون‌ها:\n";
    echo "----------------------\n";
    $updated_columns = updateColumns($pdo);
    echo "\n";

    // اضافه کردن داده‌های اولیه
    echo "📝 اضافه کردن داده‌های اولیه:\n";
    echo "-------------------------------\n";
    insertInitialData($pdo);
    echo "\n";

    // گزارش نهایی
    echo "✅ نصب دیتابیس با موفقیت تکمیل شد!\n";
    echo "=====================================\n\n";

    echo "📊 خلاصه عملیات:\n";
    echo "• تعداد تیبل‌های پردازش شده: " . count($tables) . "\n";
    echo "• تیبل‌ها: " . implode(', ', array_keys($tables)) . "\n";

    if (!empty($updated_columns)) {
        echo "• ستون‌های بروزرسانی شده: " . implode(', ', $updated_columns) . "\n";
    }

    echo "\n🎉 دیتابیس آماده استفاده است!\n";
    echo "</pre>\n";
}

// اجرای اصلی
try {
    // تشخیص محیط اجرا
    if (php_sapi_name() === 'cli') {
        // Command Line
        echo "Store Bot Database Setup\n";
        echo "========================\n";
    } else {
        // Web Browser
        echo "<!DOCTYPE html>\n";
        echo "<html dir='rtl'>\n";
        echo "<head>\n";
        echo "<meta charset='UTF-8'>\n";
        echo "<title>نصب دیتابیس Store Bot</title>\n";
        echo "<style>body{font-family:Tahoma;background:#f5f5f5;margin:20px;}</style>\n";
        echo "</head>\n";
        echo "<body>\n";
        echo "<h1>🤖 نصب دیتابیس Store Bot</h1>\n";
    }

    setupDatabase($config, $security);

    if (php_sapi_name() !== 'cli') {
        echo "</body></html>\n";
    }

} catch (Exception $e) {
    echo "❌ خطای کلی: " . $e->getMessage() . "\n";
    echo "لطفاً تنظیمات دیتابیس را بررسی کنید.\n";
}
