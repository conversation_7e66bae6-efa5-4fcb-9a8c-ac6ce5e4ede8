-- جدول برای ذخیره پرداخت‌های NOWPayments
CREATE TABLE IF NOT EXISTS `nowpayments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) NOT NULL,
  `payment_id` varchar(255) NOT NULL,
  `order_id` varchar(255) NOT NULL,
  `price_amount` decimal(10,2) NOT NULL,
  `price_currency` varchar(10) NOT NULL DEFAULT 'usd',
  `pay_amount` decimal(20,8) NOT NULL,
  `pay_currency` varchar(20) NOT NULL,
  `pay_address` text NOT NULL,
  `payment_status` varchar(50) NOT NULL DEFAULT 'waiting',
  `actually_paid` decimal(20,8) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `payment_id` (`payment_id`),
  KEY `uid` (`uid`),
  <PERSON>EY `payment_status` (`payment_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- اضافه کردن ستون جدید به جدول payment برای نوع پرداخت
ALTER TABLE `payment` ADD COLUMN `payment_type` VARCHAR(50) DEFAULT 'manual' AFTER `agency`;

-- اضافه کردن ستون‌های جدید به جدول user برای ذخیره اطلاعات موقت NOWPayments
ALTER TABLE `user` ADD COLUMN `datap` VARCHAR(255) DEFAULT NULL AFTER `datam`;
