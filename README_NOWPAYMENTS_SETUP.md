# راهنمای نصب و تنظیم NOWPayments

## مرحله 1: ایجاد حساب NOWPayments

1. به سایت [NOWPayments](https://nowpayments.io) بروید
2. روی "Sign Up" کلیک کنید و حساب کاربری ایجاد کنید
3. ایمیل خود را تأیید کنید
4. وارد پنل کاربری شوید

## مرحله 2: دریافت API Key

1. در پنل NOWPayments به بخش "API Keys" بروید
2. یک API Key جدید ایجاد کنید
3. API Key را کپی کنید

## مرحله 3: تنظیم فایل‌های پروژه

### 1. بروزرسانی فایل `nowpayments_config.php`

```php
// جایگزین کردن API Key
define('NOWPAYMENTS_API_KEY', 'YOUR_ACTUAL_API_KEY_HERE');

// برای تست از sandbox استفاده کنید
define('NOWPAYMENTS_SANDBOX', true);

// برای production مقدار بالا را false کنید
// define('NOWPAYMENTS_SANDBOX', false);
```

### 2. بروزرسانی قیمت دلار در `admin/dollarprice.php`

```php
<?php
// نرخ دلار به تومان (این مقدار باید به‌روزرسانی شود)
$dollarPrice = 65000; // نرخ فعلی دلار به تومان
echo $dollarPrice;
?>
```

## مرحله 4: اجرای اسکریپت دیتابیس

فایل `database_nowpayments.sql` را در دیتابیس خود اجرا کنید:

```sql
-- اجرای دستورات SQL برای ایجاد جداول مورد نیاز
```

## مرحله 5: تست سیستم

### تست در حالت Sandbox

1. مطمئن شوید که `NOWPAYMENTS_SANDBOX` برابر `true` است
2. در ربات روی "💳 شارژ حساب" کلیک کنید
3. "💰 پرداخت از طریق ارز دیجیتال" را انتخاب کنید
4. یکی از ارزهای موجود (TRX, USDT, DOGE) را انتخاب کنید
5. مبلغ دلخواه را وارد کنید
6. آدرس کیف پول نمایش داده می‌شود
7. روی "🔍 بررسی پرداخت" کلیک کنید

### نکات مهم برای تست

- در حالت sandbox، پرداخت‌ها واقعی نیستند
- برای تست، می‌توانید از مقادیر خاص استفاده کنید
- مستندات sandbox NOWPayments را مطالعه کنید

## مرحله 6: راه‌اندازی Production

1. `NOWPAYMENTS_SANDBOX` را `false` کنید
2. API Key production را وارد کنید
3. تست کامل انجام دهید
4. کیف پول‌های واقعی را تنظیم کنید

## ارزهای پشتیبانی شده

- **TRX (TRON)**: ارز دیجیتال ترون
- **USDT (TRC20)**: تتر روی شبکه ترون
- **DOGE (Dogecoin)**: دوج کوین

## امکانات سیستم

✅ انتخاب ارز دیجیتال  
✅ محاسبه خودکار نرخ ارز  
✅ تولید آدرس پرداخت  
✅ بررسی وضعیت پرداخت  
✅ شارژ خودکار حساب  
✅ گزارش‌گیری کامل  

## عیب‌یابی

### خطاهای رایج

1. **"API Key نامعتبر"**: API Key را بررسی کنید
2. **"خطا در اتصال"**: اتصال اینترنت را بررسی کنید
3. **"ارز پشتیبانی نمی‌شود"**: لیست ارزهای پشتیبانی شده را بررسی کنید

### لاگ‌ها

خطاها در فایل `error.txt` ذخیره می‌شوند.

## پشتیبانی

برای مشکلات فنی:
- مستندات NOWPayments: https://documenter.getpostman.com/view/7907941/S1a32n38
- پشتیبانی NOWPayments: <EMAIL>

## نکات امنیتی

⚠️ **هرگز API Key خود را در فایل‌های عمومی قرار ندهید**  
⚠️ **از HTTPS استفاده کنید**  
⚠️ **دسترسی‌ها را محدود کنید**  
⚠️ **به‌روزرسانی‌های امنیتی را نصب کنید**
